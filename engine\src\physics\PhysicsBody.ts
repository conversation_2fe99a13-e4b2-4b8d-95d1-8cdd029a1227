/**
 * 物理体组件
 * 为实体提供物理属性和行为
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { Component } from '../core/Component';
import type { Entity } from '../core/Entity';
import { PhysicsCollider } from './PhysicsCollider';

export enum BodyType {
  STATIC = 'static',
  DYNAMIC = 'dynamic',
  KINEMATIC = 'kinematic',
}

export interface PhysicsBodyOptions {
  /** 物理体类型 */
  type?: BodyType;
  /** 质量 */
  mass?: number;
  /** 是否固定旋转 */
  fixedRotation?: boolean;
  /** 线性阻尼 */
  linearDamping?: number;
  /** 角阻尼 */
  angularDamping?: number;
  /** 是否允许休眠 */
  allowSleep?: boolean;
  /** 是否触发器 */
  isTrigger?: boolean;
  /** 碰撞组 */
  collisionGroup?: number;
  /** 碰撞掩码 */
  collisionMask?: number;
  /** 材质属性 */
  material?: {
    /** 摩擦系数 */
    friction?: number;
    /** 恢复系数 */
    restitution?: number;
  };
}

export class PhysicsBody extends Component {
  /** 组件类型 */
  public static readonly type: string = 'PhysicsBody';

  /** CANNON.js物理体 */
  private body: CANNON.Body | null = null;

  /** 物理体类型 */
  private bodyType: BodyType;

  /** 质量 */
  private mass: number;

  /** 是否固定旋转 */
  private fixedRotation: boolean;

  /** 线性阻尼 */
  private linearDamping: number;

  /** 角阻尼 */
  private angularDamping: number;

  /** 是否允许休眠 */
  private allowSleep: boolean;

  /** 碰撞组 */
  private collisionGroup: number;

  /** 碰撞掩码 */
  private collisionMask: number;

  /** 物理材质 */
  private physicsMaterial: CANNON.Material | null = null;

  /** 物理世界 */
  private world: CANNON.World | null = null;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 碰撞回调 */
  private collisionCallbacks: {
    start?: (entity: Entity, contact: any) => void;
    stay?: (entity: Entity, contact: any) => void;
    end?: (entity: Entity, contact: any) => void;
    triggerEnter?: (entity: Entity, contact: any) => void;
    triggerStay?: (entity: Entity, contact: any) => void;
    triggerExit?: (entity: Entity, contact: any) => void;
  } = {};

  /**
   * 创建物理体组件
   * @param options 物理体选项
   */
  constructor(options: PhysicsBodyOptions = {}) {
    super(PhysicsBody.type);

    this.bodyType = options.type || BodyType.DYNAMIC;
    this.mass = this.bodyType === BodyType.STATIC ? 0 : (options.mass || 1);
    this.fixedRotation = options.fixedRotation || false;
    this.linearDamping = options.linearDamping || 0.01;
    this.angularDamping = options.angularDamping || 0.01;
    this.allowSleep = options.allowSleep !== undefined ? options.allowSleep : true;
    this.collisionGroup = options.collisionGroup || 1;
    this.collisionMask = options.collisionMask || -1; // 默认与所有组碰撞

    // 创建物理材质
    if (options.material) {
      this.physicsMaterial = new CANNON.Material();
      this.physicsMaterial.friction = options.material.friction !== undefined ? options.material.friction : 0.3;
      this.physicsMaterial.restitution = options.material.restitution !== undefined ? options.material.restitution : 0.3;
    }
  }

  /**
   * 初始化物理体
   * @param world 物理世界
   */
  public initialize(world: CANNON.World): void {
    if (this.initialized || !this.entity) return;

    this.world = world;

    // 创建物理体
    const bodyOptions: CANNON.BodyOptions = {
      mass: this.mass,
      fixedRotation: this.fixedRotation,
      linearDamping: this.linearDamping,
      angularDamping: this.angularDamping,
      allowSleep: this.allowSleep,
      collisionFilterGroup: this.collisionGroup,
      collisionFilterMask: this.collisionMask,
      material: this.physicsMaterial || undefined,
    };

    // 创建物理体
    this.body = new CANNON.Body(bodyOptions);

    // 设置物理体类型（使用类型断言）
    (this.body as any).type = this.getCannonBodyType();

    // 设置用户数据（使用自定义属性）
    (this.body as any).userData = { entity: this.entity };

    // 获取实体的变换组件
    const transform = this.entity.getTransform();
    if (transform) {
      // 设置初始位置
      const position = transform.getPosition();
      (this.body as any).setPosition(position.x, position.y, position.z);

      // 设置初始旋转
      const rotation = transform.getRotation();
      const quaternion = new THREE.Quaternion().setFromEuler(
        new THREE.Euler(rotation.x, rotation.y, rotation.z)
      );
      (this.body as any).setRotationQuaternion(quaternion.x, quaternion.y, quaternion.z, quaternion.w);
    }

    // 获取实体的碰撞器组件
    const collider = this.entity.getComponent<PhysicsCollider>(PhysicsCollider.type);
    if (collider) {
      // 添加碰撞形状
      const shapes = collider.getShapes();
      for (const shape of shapes) {
        this.body.addShape(shape);
      }
    } else {
      // 如果没有碰撞器，创建一个默认的盒体碰撞器
      console.warn(`实体 ${this.entity.id} 没有碰撞器组件，将使用默认盒体碰撞器`);
      const boxShape = new CANNON.Box(new CANNON.Vec3(0.5, 0.5, 0.5));
      this.body.addShape(boxShape);
    }

    // 添加到物理世界
    world.addBody(this.body);

    this.initialized = true;
  }

  /**
   * 获取CANNON.js物理体类型
   * @returns CANNON.js物理体类型
   */
  private getCannonBodyType(): number {
    switch (this.bodyType) {
      case BodyType.STATIC:
        return CANNON.Body.STATIC;
      case BodyType.DYNAMIC:
        return CANNON.Body.DYNAMIC;
      case BodyType.KINEMATIC:
        return CANNON.Body.KINEMATIC;
      default:
        return CANNON.Body.DYNAMIC;
    }
  }

  /**
   * 更新实体变换
   */
  public updateTransform(): void {
    if (!this.initialized || !this.entity || !this.body) return;

    // 获取实体的变换组件
    const transform = this.entity.getTransform();
    if (transform) {
      // 如果是动态物体，从物理引擎更新到实体
      if (this.bodyType === BodyType.DYNAMIC) {
        // 更新位置
        transform.setPosition(
          (this.body as any).getPosition().x,
          (this.body as any).getPosition().y,
          (this.body as any).getPosition().z
        );

        // 更新旋转
        if (!this.fixedRotation) {
          const quaternion = new THREE.Quaternion(
            this.body.quaternion.x,
            this.body.quaternion.y,
            this.body.quaternion.z,
            this.body.quaternion.w
          );
          const euler = new THREE.Euler().setFromQuaternion(quaternion);
          transform.setRotation(euler.x, euler.y, euler.z);
        }
      }
      // 如果是静态或运动学物体，从实体更新到物理引擎
      else if (this.bodyType === BodyType.STATIC || this.bodyType === BodyType.KINEMATIC) {
        // 更新位置
        const position = transform.getPosition();
        (this.body as any).setPosition(position.x, position.y, position.z);

        // 更新旋转
        if (!this.fixedRotation) {
          const rotation = transform.getRotation();
          const quaternion = new THREE.Quaternion().setFromEuler(
            new THREE.Euler(rotation.x, rotation.y, rotation.z)
          );
          (this.body as any).setRotationQuaternion(quaternion.x, quaternion.y, quaternion.z, quaternion.w);
        }

        // 如果是运动学物体，需要更新速度
        if (this.bodyType === BodyType.KINEMATIC) {
          this.body.velocity.set(0, 0, 0);
          this.body.angularVelocity.set(0, 0, 0);
        }
      }
    }
  }

  /**
   * 应用力
   * @param force 力向量
   * @param worldPoint 世界坐标中的作用点（可选）
   */
  public applyForce(force: { x: number; y: number; z: number }, worldPoint?: { x: number; y: number; z: number }): void {
    if (!this.initialized || !this.body) return;

    const forceVec = new CANNON.Vec3(force.x, force.y, force.z);

    if (worldPoint) {
      const pointVec = new CANNON.Vec3(worldPoint.x, worldPoint.y, worldPoint.z);
      this.body.applyForce(forceVec, pointVec);
    } else {
      this.body.applyForce(forceVec, this.body.position);
    }
  }

  /**
   * 应用冲量
   * @param impulse 冲量向量
   * @param worldPoint 世界坐标中的作用点（可选）
   */
  public applyImpulse(impulse: { x: number; y: number; z: number }, worldPoint?: { x: number; y: number; z: number }): void {
    if (!this.initialized || !this.body) return;

    const impulseVec = new CANNON.Vec3(impulse.x, impulse.y, impulse.z);

    if (worldPoint) {
      const pointVec = new CANNON.Vec3(worldPoint.x, worldPoint.y, worldPoint.z);
      this.body.applyImpulse(impulseVec, pointVec);
    } else {
      this.body.applyImpulse(impulseVec, this.body.position);
    }
  }

  /**
   * 设置线性速度
   * @param velocity 速度向量
   */
  public setLinearVelocity(velocity: { x: number; y: number; z: number }): void {
    if (!this.initialized || !this.body) return;

    this.body.velocity.set(velocity.x, velocity.y, velocity.z);
  }

  /**
   * 获取线性速度
   * @returns 速度向量
   */
  public getLinearVelocity(): { x: number; y: number; z: number } {
    if (!this.initialized || !this.body) return { x: 0, y: 0, z: 0 };

    return {
      x: this.body.velocity.x,
      y: this.body.velocity.y,
      z: this.body.velocity.z,
    };
  }

  /**
   * 设置角速度
   * @param velocity 角速度向量
   */
  public setAngularVelocity(velocity: { x: number; y: number; z: number }): void {
    if (!this.initialized || !this.body || this.fixedRotation) return;

    this.body.angularVelocity.set(velocity.x, velocity.y, velocity.z);
  }

  /**
   * 获取角速度
   * @returns 角速度向量
   */
  public getAngularVelocity(): { x: number; y: number; z: number } {
    if (!this.initialized || !this.body || this.fixedRotation) return { x: 0, y: 0, z: 0 };

    return {
      x: this.body.angularVelocity.x,
      y: this.body.angularVelocity.y,
      z: this.body.angularVelocity.z,
    };
  }

  /**
   * 获取速度（getLinearVelocity的别名）
   * @returns 速度向量
   */
  public getVelocity(): THREE.Vector3 {
    const velocity = this.getLinearVelocity();
    return new THREE.Vector3(velocity.x, velocity.y, velocity.z);
  }

  /**
   * 设置位置
   * @param position 位置向量
   */
  public setPosition(position: { x: number; y: number; z: number }): void {
    if (!this.initialized || !this.body) return;

    this.body.position.set(position.x, position.y, position.z);
  }

  /**
   * 获取位置
   * @returns 位置向量
   */
  public getPosition(): { x: number; y: number; z: number } {
    if (!this.initialized || !this.body) return { x: 0, y: 0, z: 0 };

    return {
      x: this.body.position.x,
      y: this.body.position.y,
      z: this.body.position.z,
    };
  }

  /**
   * 获取角速度（返回THREE.Vector3类型）
   * @returns 角速度向量
   */
  public getAngularVelocityVector3(): THREE.Vector3 {
    const angularVelocity = this.getAngularVelocity();
    return new THREE.Vector3(angularVelocity.x, angularVelocity.y, angularVelocity.z);
  }

  /**
   * 设置位置（接受THREE.Vector3类型）
   * @param position 位置向量
   */
  public setPositionVector3(position: THREE.Vector3): void {
    this.setPosition({ x: position.x, y: position.y, z: position.z });
  }

  /**
   * 获取位置（返回THREE.Vector3类型）
   * @returns 位置向量
   */
  public getPositionVector3(): THREE.Vector3 {
    const position = this.getPosition();
    return new THREE.Vector3(position.x, position.y, position.z);
  }

  /**
   * 设置碰撞回调
   * @param callbacks 碰撞回调函数
   */
  public setCollisionCallbacks(callbacks: {
    start?: (entity: Entity, contact: any) => void;
    stay?: (entity: Entity, contact: any) => void;
    end?: (entity: Entity, contact: any) => void;
    triggerEnter?: (entity: Entity, contact: any) => void;
    triggerStay?: (entity: Entity, contact: any) => void;
    triggerExit?: (entity: Entity, contact: any) => void;
  }): void {
    this.collisionCallbacks = callbacks;
  }

  /**
   * 碰撞开始回调
   * @param otherEntity 碰撞的另一个实体
   * @param contact 碰撞信息
   */
  public onCollisionStart(otherEntity: Entity, contact: any): void {
    if (this.collisionCallbacks.start) {
      this.collisionCallbacks.start(otherEntity, contact);
    }
  }

  /**
   * 碰撞持续回调
   * @param otherEntity 碰撞的另一个实体
   * @param contact 碰撞信息
   */
  public onCollisionStay(otherEntity: Entity, contact: any): void {
    if (this.collisionCallbacks.stay) {
      this.collisionCallbacks.stay(otherEntity, contact);
    }
  }

  /**
   * 碰撞结束回调
   * @param otherEntity 碰撞的另一个实体
   * @param contact 碰撞信息
   */
  public onCollisionEnd(otherEntity: Entity, contact: any): void {
    if (this.collisionCallbacks.end) {
      this.collisionCallbacks.end(otherEntity, contact);
    }
  }

  /**
   * 触发器进入回调
   * @param otherEntity 碰撞的另一个实体
   * @param contact 碰撞信息
   */
  public onTriggerEnter(otherEntity: Entity, contact: any): void {
    if (this.collisionCallbacks.triggerEnter) {
      this.collisionCallbacks.triggerEnter(otherEntity, contact);
    }
  }

  /**
   * 触发器停留回调
   * @param otherEntity 碰撞的另一个实体
   * @param contact 碰撞信息
   */
  public onTriggerStay(otherEntity: Entity, contact: any): void {
    if (this.collisionCallbacks.triggerStay) {
      this.collisionCallbacks.triggerStay(otherEntity, contact);
    }
  }

  /**
   * 触发器离开回调
   * @param otherEntity 碰撞的另一个实体
   * @param contact 碰撞信息
   */
  public onTriggerExit(otherEntity: Entity, contact: any): void {
    if (this.collisionCallbacks.triggerExit) {
      this.collisionCallbacks.triggerExit(otherEntity, contact);
    }
  }

  /**
   * 获取CANNON.js物理体
   * @returns CANNON.js物理体
   */
  public getCannonBody(): CANNON.Body | null {
    return this.body;
  }

  /**
   * 获取物理体类型
   * @returns 物理体类型
   */
  public getBodyType(): BodyType {
    return this.bodyType;
  }

  /**
   * 设置物理体类型
   * @param type 物理体类型
   */
  public setBodyType(type: BodyType): void {
    if (this.bodyType === type) return;

    this.bodyType = type;

    if (this.body) {
      // 更新质量
      if (type === BodyType.STATIC) {
        this.mass = 0;
      } else if (this.mass === 0) {
        this.mass = 1;
      }

      // 更新物理体类型（使用类型断言）
      (this.body as any).type = this.getCannonBodyType();
      this.body.mass = this.mass;
      this.body.updateMassProperties();
    }
  }

  /**
   * 销毁物理体
   */
  public dispose(): void {
    if (this.initialized && this.body && this.world) {
      this.world.removeBody(this.body);
      this.body = null;
      this.world = null;
      this.initialized = false;
    }

    // 调用基类的dispose方法
    super.dispose();
  }
}
